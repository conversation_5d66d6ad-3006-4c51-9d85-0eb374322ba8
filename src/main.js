// Main application logic using Meteora CP-AMM SDK
const { CpAmm } = require('@meteora-ag/cp-amm-sdk');
const { BN } = require('@coral-xyz/anchor');
const { Transaction, sendAndConfirmTransaction } = require('@solana/web3.js');

// Import configuration
const config = require('./config');

// Import utilities
const { createConnection } = require('./utils/connection');
const { createWallet, createPublicKey } = require('./utils/wallet');

// Import services
const { getSolBalance } = require('./services/balances');
const { createSwapInstructions } = require('./services/swap');
const { createAddLiquidityInstructions } = require('./services/liquidity');

/**
 * Main function that orchestrates the entire process using Meteora CP-AMM
 */
async function main() {
  try {
    // Create Solana connection
    const solConnection = createConnection();

    // Create wallet from private key
    const wallet = createWallet();

    // Initialize the Meteora CP-AMM SDK
    const cpAmmSdk = new CpAmm(solConnection);

    // Create pool public key
    const poolPubkey = createPublicKey(config.POOL_ADDRESS);
    console.log(`Pool address: ${poolPubkey.toString()}`);

    // Fetch pool state to get token information
    const poolState = await cpAmmSdk.fetchPoolState(poolPubkey);
    console.log(`Token A mint: ${poolState.tokenAMint.toString()}`);
    console.log(`Token B mint: ${poolState.tokenBMint.toString()}`);

    // Get initial SOL balance
    const initialSolBalance = await getSolBalance(solConnection, wallet.publicKey);

    // Note: Meteora doesn't have creator vaults like PumpSwap
    // Instead, we'll skip the vault checking step for now
    // In a real implementation, you might want to check for existing positions
    // and claim any available fees from those positions

    let updatedSolBalance = initialSolBalance;
    let instructionArray = [];

    console.log('Meteora CP-AMM does not have creator vaults. Continuing with available balance...');

    // Constants for SOL calculations (in lamports)
    const MIN_BALANCE_LAMPORTS = 0.15 * 1e9; // 0.15 SOL in lamports
    const RESERVE_FOR_FEES_LAMPORTS = 0.05 * 1e9; // 0.05 SOL in lamports

    // Check if we have enough balance to swap (minimum 0.15 SOL)
    if (updatedSolBalance < MIN_BALANCE_LAMPORTS) {
      console.log(`Not enough balance to swap. Current balance: ${updatedSolBalance / 1e9} SOL, minimum required: 0.15 SOL. Exiting...`);
      return;
    }

    // Calculate available amount for operations (leaving 0.05 SOL for fees)
    const availableAmount = updatedSolBalance - RESERVE_FOR_FEES_LAMPORTS;

    // Calculate half of the available amount to swap
    const halfAvailableAmount = Math.floor(availableAmount / 2);

    if (halfAvailableAmount <= 0) {
      console.log('Not enough balance to swap after reserving for fees. Exiting...');
      return;
    }

    console.log(`Using ${halfAvailableAmount / 1e9} SOL for swap (half of available balance after reserving for fees)...`);
    console.log(`Swap amount in lamports: ${halfAvailableAmount}`);

    // Step 3: Swap half of the available amount into the token
    const halfAvailableAmountBN = new BN(halfAvailableAmount);

    // Create instructions for Swap
    const { instructions: swapInstructions, expectedOutputAmount } = await createSwapInstructions(
      cpAmmSdk,
      poolPubkey,
      halfAvailableAmountBN,
      config.SLIPPAGE,
      wallet.publicKey,
      poolState
    );
    instructionArray = instructionArray.concat(swapInstructions);

    // Step 4: Add liquidity with the remaining SOL and the tokens we just swapped
    const remainingSolAmount = new BN(halfAvailableAmount); // Use the other half for liquidity
    const { instructions: depositInstructions, liquidityDelta, positionNft, positionNftKeypair } = await createAddLiquidityInstructions(
      cpAmmSdk,
      poolPubkey,
      expectedOutputAmount, // Token amount from swap
      remainingSolAmount,   // Remaining SOL amount
      config.SLIPPAGE,
      wallet.publicKey,
      poolState
    );
    instructionArray = instructionArray.concat(depositInstructions);

    // Create the transaction
    const tx = new Transaction();
    tx.add(...instructionArray);

    // Get recent blockhash
    const blockhash = await solConnection.getLatestBlockhash();
    tx.recentBlockhash = blockhash.blockhash;
    tx.feePayer = wallet.publicKey;

    // Sign the transaction with both wallet and position NFT keypair
    tx.sign(wallet, positionNftKeypair);

    // Send and confirm the transaction
    const txHash = await sendAndConfirmTransaction(solConnection, tx, [wallet, positionNftKeypair]);

    // Get final SOL balance
    const finalSolBalance = await getSolBalance(solConnection, wallet.publicKey);
    console.log(`TX hash: ${txHash}`);
    console.log(`Final balance: ${finalSolBalance}`);
    console.log(`Position NFT created: ${positionNft.toString()}`);
    console.log(`Liquidity delta: ${liquidityDelta.toString()}`);

  } catch (error) {
    console.error('Error:', error);
    console.error('Error details:', error.stack);
  }
}

module.exports = main;
