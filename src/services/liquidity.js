// Liquidity operations using Meteora CP-AMM SDK
const { Transaction, sendAndConfirmTransaction, Keypair } = require('@solana/web3.js');
const { TOKEN_PROGRAM_ID } = require('@solana/spl-token');
const { BN } = require('@coral-xyz/anchor');
const { MIN_SQRT_PRICE, MAX_SQRT_PRICE } = require('@meteora-ag/cp-amm-sdk');

/**
 * Checks if we need to add to LP based on available balances
 * @param {BN} solBalance - SOL balance
 * @param {BN} tokenBalance - Token balance
 * @returns {boolean} True if we should add to LP
 */
function shouldAddToLP(solBalance, tokenBalance) {
  // Implement your logic here to determine if LP addition is needed
  // For now, we'll assume we should add if both balances are positive
  return !solBalance.isZero() && !tokenBalance.isZero();
}

/**
 * Creates add liquidity instructions using Meteora CP-AMM
 * Note: Meteora uses position-based liquidity, so this creates a new position and adds liquidity
 * @param {CpAmm} cpAmmSdk - Meteora CP-AMM SDK instance
 * @param {PublicKey} poolPubkey - Pool public key
 * @param {BN} tokenAmount - Token amount to add
 * @param {BN} solAmount - SOL amount to add
 * @param {number} slippage - Slippage tolerance (e.g., 0.01 for 1%)
 * @param {PublicKey} walletPublicKey - Wallet public key
 * @param {Object} poolState - Pool state from fetchPoolState
 * @returns {Promise<{instructions: Array, liquidityDelta: BN, positionNft: PublicKey}>} Instructions and liquidity details
 */
async function createAddLiquidityInstructions(cpAmmSdk, poolPubkey, tokenAmount, solAmount, slippage, walletPublicKey, poolState) {
  console.log('Creating add liquidity instructions...');

  // Calculate liquidity delta based on token amounts
  const liquidityDelta = await cpAmmSdk.getLiquidityDelta({
    maxAmountTokenA: tokenAmount,
    maxAmountTokenB: solAmount,
    sqrtPrice: poolState.sqrtPrice,
    sqrtMinPrice: MIN_SQRT_PRICE,
    sqrtMaxPrice: MAX_SQRT_PRICE
  });

  console.log(`Calculated liquidity delta: ${liquidityDelta.toString()}`);

  // Generate a new position NFT mint
  const positionNft = Keypair.generate();

  // Create position and add liquidity in one transaction
  const addLiquidityTxBuilder = await cpAmmSdk.createPositionAndAddLiquidity({
    owner: walletPublicKey,
    pool: poolPubkey,
    positionNft: positionNft.publicKey,
    liquidityDelta: liquidityDelta,
    maxAmountTokenA: tokenAmount,
    maxAmountTokenB: solAmount,
    tokenAAmountThreshold: new BN(0), // Minimum acceptable amounts (slippage protection)
    tokenBAmountThreshold: new BN(0),
    tokenAMint: poolState.tokenAMint,
    tokenBMint: poolState.tokenBMint,
    tokenAProgram: TOKEN_PROGRAM_ID,
    tokenBProgram: TOKEN_PROGRAM_ID
  });

  // Get the transaction and extract instructions
  const addLiquidityTx = await addLiquidityTxBuilder.transaction();

  return {
    instructions: addLiquidityTx.instructions,
    liquidityDelta,
    positionNft: positionNft.publicKey,
    positionNftKeypair: positionNft // Need this for signing
  };
}

/**
 * Adds liquidity to the pool using Meteora CP-AMM
 * @param {CpAmm} cpAmmSdk - Meteora CP-AMM SDK instance
 * @param {Connection} connection - Solana connection
 * @param {PublicKey} poolPubkey - Pool public key
 * @param {BN} tokenAmount - Token amount to add
 * @param {BN} solAmount - SOL amount to add
 * @param {number} slippage - Slippage tolerance (e.g., 0.01 for 1%)
 * @param {Keypair} wallet - Wallet keypair
 * @param {Object} poolState - Pool state from fetchPoolState
 * @returns {Promise<{txHash: string, liquidityDelta: BN, positionNft: PublicKey}>} Transaction hash and liquidity details
 */
async function addLiquidity(cpAmmSdk, connection, poolPubkey, tokenAmount, solAmount, slippage, wallet, poolState) {
  console.log('Adding liquidity to the pool...');

  // Create add liquidity instructions
  const { instructions: depositInstructions, liquidityDelta, positionNft, positionNftKeypair } = await createAddLiquidityInstructions(
    cpAmmSdk,
    poolPubkey,
    tokenAmount,
    solAmount,
    slippage,
    wallet.publicKey,
    poolState
  );

  // Create a new transaction
  const depositTx = new Transaction();

  // Add the instructions
  depositTx.add(...depositInstructions);

  // Get recent blockhash
  const depositBlockhash = await connection.getLatestBlockhash();
  depositTx.recentBlockhash = depositBlockhash.blockhash;
  depositTx.feePayer = wallet.publicKey;

  // Sign the transaction with both wallet and position NFT keypair
  depositTx.sign(wallet, positionNftKeypair);

  // Send and confirm the transaction
  const depositTxHash = await sendAndConfirmTransaction(connection, depositTx, [wallet, positionNftKeypair]);
  console.log(`Liquidity added successfully! Transaction hash: ${depositTxHash}`);
  console.log(`Position NFT created: ${positionNft.toString()}`);

  return {
    txHash: depositTxHash,
    liquidityDelta,
    positionNft
  };
}

module.exports = {
  shouldAddToLP,
  createAddLiquidityInstructions,
  addLiquidity
};
