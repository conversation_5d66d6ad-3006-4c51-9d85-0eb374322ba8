// Swap operations using Meteora CP-AMM SDK
const { Transaction, sendAndConfirmTransaction } = require('@solana/web3.js');
const { TOKEN_PROGRAM_ID } = require('@solana/spl-token');
const { BN } = require('@coral-xyz/anchor');

/**
 * Creates swap instructions for SOL to token swap using Meteora CP-AMM
 * @param {CpAmm} cpAmmSdk - Meteora CP-AMM SDK instance
 * @param {PublicKey} poolPubkey - Pool public key
 * @param {BN} amount - Amount to swap in lamports
 * @param {number} slippage - Slippage tolerance (e.g., 0.01 for 1%)
 * @param {PublicKey} walletPublicKey - Wallet public key
 * @param {Object} poolState - Pool state from fetchPoolState
 * @returns {Promise<{instructions: Array, expectedOutputAmount: BN}>} Swap instructions and expected output amount
 */
async function createSwapInstructions(cpAmmSdk, poolPubkey, amount, slippage, walletPublicKey, poolState) {
  // Ensure amount is a BN and log it for debugging
  if (!(amount instanceof BN)) {
    console.log(`Warning: amount is not a BN instance, converting from: ${typeof amount}, value: ${amount}`);
    amount = new BN(amount.toString());
  }

  console.log(`Creating swap instructions for ${amount.toString()} SOL lamports (${amount.toNumber() / 1e9} SOL)...`);

  // Get current time and slot for quote calculation
  const currentTime = Math.floor(Date.now() / 1000);
  const currentSlot = 0; // You might want to get this from connection if needed

  // Get quote for the swap (SOL is typically tokenB in most pools)
  const quote = await cpAmmSdk.getQuote({
    inAmount: amount,
    inputTokenMint: poolState.tokenBMint, // Assuming SOL is tokenB
    slippage: slippage * 100, // Convert to percentage (e.g., 0.01 -> 1%)
    poolState: poolState,
    currentTime: currentTime,
    currentSlot: currentSlot
  });

  console.log(`Expected output amount from swap: ${quote.swapOutAmount.toString()}`);
  console.log(`Minimum output amount (with slippage): ${quote.minSwapOutAmount.toString()}`);
  console.log(`Total fee: ${quote.totalFee.toString()}`);
  console.log(`Price impact: ${quote.priceImpact.toFixed(4)}%`);

  // Create swap transaction builder
  const swapTxBuilder = await cpAmmSdk.swap({
    payer: walletPublicKey,
    pool: poolPubkey,
    inputTokenMint: poolState.tokenBMint, // SOL
    outputTokenMint: poolState.tokenAMint, // Target token
    amountIn: amount,
    minimumAmountOut: quote.minSwapOutAmount,
    tokenAMint: poolState.tokenAMint,
    tokenBMint: poolState.tokenBMint,
    tokenAVault: poolState.tokenAVault,
    tokenBVault: poolState.tokenBVault,
    tokenAProgram: TOKEN_PROGRAM_ID,
    tokenBProgram: TOKEN_PROGRAM_ID,
    referralTokenAccount: null
  });

  // Get the transaction and extract instructions
  const swapTx = await swapTxBuilder.transaction();

  return {
    instructions: swapTx.instructions,
    expectedOutputAmount: quote.swapOutAmount
  };
}

/**
 * Swaps SOL for tokens using Meteora CP-AMM
 * @param {CpAmm} cpAmmSdk - Meteora CP-AMM SDK instance
 * @param {Connection} connection - Solana connection
 * @param {PublicKey} poolPubkey - Pool public key
 * @param {BN} amount - Amount to swap in lamports
 * @param {number} slippage - Slippage tolerance (e.g., 0.01 for 1%)
 * @param {Keypair} wallet - Wallet keypair
 * @param {Object} poolState - Pool state from fetchPoolState
 * @returns {Promise<{txHash: string, expectedOutputAmount: BN}>} Transaction hash and expected output amount
 */
async function swapSolForToken(cpAmmSdk, connection, poolPubkey, amount, slippage, wallet, poolState) {
  console.log(`Swapping ${amount.toString()} SOL lamports (${amount.toNumber() / 1e9} SOL) for tokens...`);

  // Create swap instructions
  const { instructions: swapInstructions, expectedOutputAmount } = await createSwapInstructions(
    cpAmmSdk,
    poolPubkey,
    amount,
    slippage,
    wallet.publicKey,
    poolState
  );

  // Create a new transaction
  const swapTx = new Transaction();

  // Add the instructions
  swapTx.add(...swapInstructions);

  // Get recent blockhash
  const swapBlockhash = await connection.getLatestBlockhash();
  swapTx.recentBlockhash = swapBlockhash.blockhash;
  swapTx.feePayer = wallet.publicKey;

  // Sign the transaction
  swapTx.sign(wallet);

  // Send and confirm the transaction
  const signature = await sendAndConfirmTransaction(connection, swapTx, [wallet]);
  console.log(`Swap transaction confirmed: ${signature}`);

  return {
    txHash: signature,
    expectedOutputAmount
  };
}

module.exports = {
  createSwapInstructions,
  swapSolForToken
};
