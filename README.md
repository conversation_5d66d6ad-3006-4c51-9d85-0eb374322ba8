# Slurp - Simple Solana Token Script

A barebones Node.js script for interacting with Solana tokens using Meteora's CP-AMM SDK.

## Setup

1. Install dependencies:
   ```
   npm install
   ```

2. Configure your environment variables:
   - Create a `.env` file in the root directory with the following variables:
     ```
     PRIVATE_KEY=your_private_key_here
     RPC_ENDPOINT=https://api.mainnet-beta.solana.com
     POOL_ADDRESS=your_meteora_pool_address_here
     SLIPPAGE=0.01
     ```
   - You can copy the `.env.example` file to create your `.env` file:
     ```
     cp .env.example .env
     ```
   - Then edit the values with your actual information.

## Usage

Run the script:
```
node index.js
```

## Features

The script performs the following operations using Meteora's CP-AMM:
- Initializing the Meteora CP-AMM SDK
- Fetching pool state and token information
- Swapping SOL for tokens using Meteora's swap functionality
- Creating position-based liquidity with NFT positions
- Adding liquidity to the pool with the swapped tokens

## Customization

Modify the `index.js` file to add more functionality as needed. The Meteora CP-AMM SDK provides many more features that you can explore:

- `@meteora-ag/cp-amm-sdk`: Core functionality for concentrated liquidity pools, swaps, and position management
- Position-based liquidity with NFT ownership
- Fee claiming from positions
- Reward claiming mechanisms

## Important Notes

### Meteora CP-AMM vs PumpSwap Differences

This script has been migrated from PumpSwap to Meteora's CP-AMM, which has several key differences:

1. **Position-based Liquidity**: Meteora uses NFT-based positions instead of simple LP tokens
2. **No Creator Vaults**: Meteora doesn't have the same creator vault concept as PumpSwap
3. **Concentrated Liquidity**: Meteora supports concentrated liquidity ranges
4. **Fee Structure**: Different fee claiming mechanisms through positions

### Pool Requirements

Make sure your `POOL_ADDRESS` points to a valid Meteora CP-AMM pool. You can find available pools on:
- [Meteora App](https://app.meteora.ag/)
- Meteora's documentation and pool listings

## Security Notes

- Never hardcode your private key in the script for production use
- Use environment variables or a secure secret management solution
- Be careful when running scripts that interact with real tokens
- Always test with small amounts first
- Understand that position NFTs will be created and managed by the script
